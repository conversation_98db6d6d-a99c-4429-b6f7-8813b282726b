<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Card Visit Generator - Dual Camera</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f8fa;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .header p {
            color: #7f8c8d;
            font-size: 16px;
        }
        .camera-container {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            margin-bottom: 30px;
        }
        .camera-box {
            flex: 1;
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .camera-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            text-align: center;
            color: #2c3e50;
        }
        .camera-feed {
            width: 100%;
            border-radius: 8px;
            border: 1px solid #ddd;
            max-height: 360px;
            object-fit: cover;
        }
        .btn {
            background: linear-gradient(135deg, #28cdea 0%, #2aa8d0 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
            margin-top: 10px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-secondary {
            background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);
        }
        .btn-danger {
            background: linear-gradient(135deg, #ef5350 0%, #e53935 100%);
        }
        .btn-container {
            text-align: center;
            margin-top: 15px;
            display: flex;
            justify-content: center;
            gap: 10px;
        }
        .focus-control {
            margin-top: 15px;
            text-align: center;
        }
        .focus-control label {
            display: block;
            margin-bottom: 5px;
            color: #2c3e50;
        }
        .result-container {
            margin-top: 30px;
        }
        .result-image {
            max-width: 100%;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .preview-container {
            margin-top: 15px;
            text-align: center;
        }
        .preview-image {
            max-width: 100%;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .hidden {
            display: none;
        }
        .status-bar {
            background-color: #e9f7fe;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }
        .workflow-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            padding: 0 10px;
        }
        .step {
            text-align: center;
            flex: 1;
            position: relative;
        }
        .step-number {
            width: 30px;
            height: 30px;
            background-color: #bdc3c7;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
        }
        .step.active .step-number {
            background-color: #3498db;
        }
        .step.completed .step-number {
            background-color: #2ecc71;
        }
        .step-title {
            font-size: 14px;
            color: #7f8c8d;
        }
        .step.active .step-title {
            color: #3498db;
            font-weight: bold;
        }
        .step.completed .step-title {
            color: #2ecc71;
        }
        .step-line {
            position: absolute;
            top: 15px;
            right: -50%;
            width: 100%;
            height: 2px;
            background-color: #bdc3c7;
            z-index: -1;
        }
        .step:last-child .step-line {
            display: none;
        }
        .step.completed .step-line {
            background-color: #2ecc71;
        }
        .main-controls {
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AI Card Visit Generator</h1>
            <p>Chụp ảnh thẻ và khuôn mặt để tạo card visit AI</p>
        </div>
        
        <div class="status-bar" id="status">
            Vui lòng kiểm tra kết nối camera trước khi bắt đầu.
        </div>
        
        <div class="workflow-steps">
            <div class="step active" id="step1">
                <div class="step-number">1</div>
                <div class="step-line"></div>
                <div class="step-title">Chụp ảnh</div>
            </div>
            <div class="step" id="step2">
                <div class="step-number">2</div>
                <div class="step-line"></div>
                <div class="step-title">Trích xuất thông tin</div>
            </div>
            <div class="step" id="step3">
                <div class="step-number">3</div>
                <div class="step-line"></div>
                <div class="step-title">Xác nhận thông tin</div>
            </div>
            <div class="step" id="step4">
                <div class="step-number">4</div>
                <div class="step-line"></div>
                <div class="step-title">Chụp khuôn mặt</div>
            </div>
            <div class="step" id="step5">
                <div class="step-number">5</div>
                <div class="step-title">Tạo ảnh AI</div>
            </div>
        </div>
        
        <div class="main-controls">
            <button id="check-btn" class="btn" onclick="checkCameras()">Kiểm tra camera</button>
            <button id="start-session-btn" class="btn btn-secondary" onclick="startSession()">Bắt đầu phiên mới</button>
        </div>
        
        <div class="camera-container">
            <div class="camera-box">
                <div class="camera-title">Camera 0 (Logitech - Card)</div>
                <img id="camera0" class="camera-feed" src="/video_feed/0" alt="Camera 0">
                <div class="focus-control">
                    <label for="focus-slider">Điều chỉnh độ nét (Focus):</label>
                    <input type="range" id="focus-slider" min="0" max="255" value="80" oninput="adjustFocus(this.value)">
                    <span id="focus-value">80</span>
                </div>
                <div class="btn-container">
                    <button id="capture0-btn" class="btn" onclick="captureImage(0)">Chụp ảnh thẻ</button>
                    <button id="retake0-btn" class="btn btn-danger hidden" onclick="retakeImage(0)">Chụp lại</button>
                </div>
                <div id="camera0-preview" class="preview-container hidden"></div>
            </div>
            
            <div class="camera-box">
                <div class="camera-title">Camera 1 (Laptop - Face)</div>
                <img id="camera1" class="camera-feed" src="/video_feed/1" alt="Camera 1">
                <div class="btn-container">
                    <button id="capture1-btn" class="btn" onclick="captureImage(1)">Chụp khuôn mặt</button>
                    <button id="retake1-btn" class="btn btn-danger hidden" onclick="retakeImage(1)">Chụp lại</button>
                </div>
                <div id="camera1-preview" class="preview-container hidden"></div>
            </div>
        </div>
        
        <div class="result-container hidden" id="result-container">
            <h2>Kết quả tạo ảnh AI</h2>
            <div id="result-image"></div>
            <div class="btn-container">
                <button class="btn" onclick="downloadImage()">Tải xuống</button>
                <button class="btn btn-secondary" onclick="startNewSession()">Phiên mới</button>
            </div>
        </div>
    </div>

    <script>
        // Biến lưu trữ thông tin phiên
        let sessionId = null;
        let capturedImages = [null, null];
        let currentStep = 1;
        
        // Kiểm tra camera khi trang được tải
        window.onload = function() {
            checkCameras();
            
            // Cập nhật giá trị focus ban đầu
            document.getElementById('focus-value').textContent = document.getElementById('focus-slider').value;
        };
        
        function checkCameras() {
            document.getElementById('status').innerHTML = 'Đang kiểm tra camera...';
            
            fetch('/check_cameras')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('status').innerHTML = data.message;
                    
                    // Nếu camera không khả dụng, hiển thị thông báo
                    if (!data.camera0_status) {
                        document.getElementById('camera0').src = '';
                        document.getElementById('camera0').alt = 'Camera 0 không khả dụng';
                        document.getElementById('capture0-btn').disabled = true;
                    }
                    
                    if (!data.camera1_status) {
                        document.getElementById('camera1').src = '';
                        document.getElementById('camera1').alt = 'Camera 1 không khả dụng';
                        document.getElementById('capture1-btn').disabled = true;
                    }
                })
                .catch(error => {
                    document.getElementById('status').innerHTML = 'Lỗi khi kiểm tra camera: ' + error;
                });
        }
        
        function adjustFocus(value) {
            document.getElementById('focus-value').textContent = value;
            
            fetch('/adjust_focus', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ focus_value: parseInt(value) })
            })
            .then(response => response.json())
            .then(data => {
                console.log('Focus adjusted:', data);
                if (data.status === 'success') {
                    document.getElementById('status').innerHTML = data.message;
                }
            })
            .catch(error => {
                console.error('Error adjusting focus:', error);
                document.getElementById('status').innerHTML = 'Lỗi khi điều chỉnh focus: ' + error;
            });
        }
        
        function startSession() {
            fetch('/start_session', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    sessionId = data.session_id;
                    document.getElementById('status').innerHTML = 'Phiên mới đã bắt đầu. Vui lòng chụp ảnh thẻ.';
                    updateStep(1);
                } else {
                    document.getElementById('status').innerHTML = 'Lỗi khi bắt đầu phiên: ' + data.error;
                }
            })
            .catch(error => {
                console.error('Error starting session:', error);
                document.getElementById('status').innerHTML = 'Lỗi khi bắt đầu phiên';
            });
        }
        
        function updateStep(step) {
            // Reset all steps
            for (let i = 1; i <= 5; i++) {
                const stepElement = document.getElementById(`step${i}`);
                stepElement.classList.remove('active', 'completed');
                
                if (i < step) {
                    stepElement.classList.add('completed');
                } else if (i === step) {
                    stepElement.classList.add('active');
                }
            }
            
            currentStep = step;
        }
        
        function captureImage(cameraId) {
            fetch(`/capture_step/${cameraId}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    capturedImages[cameraId] = data.image_path;
                    
                    // Hiển thị ảnh đã chụp
                    const resultDiv = document.getElementById(`camera${cameraId}-preview`);
                    resultDiv.innerHTML = `<img src="/${data.image_path}" class="preview-image">`;
                    resultDiv.classList.remove('hidden');
                    
                    // Cập nhật trạng thái nút
                    document.getElementById(`capture${cameraId}-btn`).classList.add('hidden');
                    document.getElementById(`retake${cameraId}-btn`).classList.remove('hidden');
                    
                    // Cập nhật trạng thái workflow
                    if (cameraId === 0) {
                        document.getElementById('status').innerHTML = 'Đã chụp ảnh thẻ. Tiếp theo, hệ thống sẽ trích xuất thông tin.';
                        processCardImage();
                    } else {
                        document.getElementById('status').innerHTML = 'Đã chụp khuôn mặt. Tiếp theo, hệ thống sẽ tạo ảnh AI.';
                        generateAIImage();
                    }
                } else {
                    document.getElementById('status').innerHTML = 'Lỗi khi chụp ảnh: ' + data.message;
                }
            })
            .catch(error => {
                console.error('Error capturing image:', error);
                document.getElementById('status').innerHTML = 'Lỗi khi chụp ảnh';
            });
        }
        
        function retakeImage(cameraId) {
            // Nếu có ảnh đã chụp, gửi yêu cầu xóa
            if (capturedImages[cameraId]) {
                fetch(`/retake_image/${cameraId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ previous_image: capturedImages[cameraId] })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // Reset trạng thái
                        capturedImages[cameraId] = null;
                        document.getElementById(`camera${cameraId}-preview`).classList.add('hidden');
                        document.getElementById(`capture${cameraId}-btn`).classList.remove('hidden');
                        document.getElementById(`retake${cameraId}-btn`).classList.add('hidden');
                        
                        document.getElementById('status').innerHTML = 'Sẵn sàng chụp lại ảnh';
                        
                        // Cập nhật trạng thái workflow
                        if (cameraId === 0) {
                            updateStep(1);
                        } else {
                            updateStep(4);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error retaking image:', error);
                    document.getElementById('status').innerHTML = 'Lỗi khi chuẩn bị chụp lại';
                });
            }
        }
        
        function processCardImage() {
            if (!capturedImages[0] || !sessionId) {
                document.getElementById('status').innerHTML = 'Không có ảnh thẻ hoặc phiên không hợp lệ';
                return;
            }
            
            updateStep(2);
            document.getElementById('status').innerHTML = 'Đang trích xuất thông tin từ ảnh thẻ...';
            
            // Gửi ảnh thẻ để xử lý
            fetch('/upload_card', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ card_image: capturedImages[0] })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('status').innerHTML = 'Đã tải lên ảnh thẻ thành công. Đang trích xuất thông tin...';
                    
                    // Tiếp tục trích xuất thông tin
                    extractCardInfo();
                } else {
                    document.getElementById('status').innerHTML = 'Lỗi khi tải lên ảnh thẻ: ' + data.error;
                }
            })
            .catch(error => {
                console.error('Error uploading card:', error);
                document.getElementById('status').innerHTML = 'Lỗi khi tải lên ảnh thẻ';
            });
        }
        
        function extractCardInfo() {
            fetch('/extract_card_info', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateStep(3);
                    document.getElementById('status').innerHTML = 'Đã trích xuất thông tin thành công. Vui lòng chụp khuôn mặt.';
                    
                    // Hiển thị thông tin đã trích xuất
                    const cardInfo = data.card_info;
                    let infoHTML = '<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 15px;">';
                    infoHTML += `<p><strong>Tên:</strong> ${cardInfo.name || 'N/A'}</p>`;
                    infoHTML += `<p><strong>Chức vụ:</strong> ${cardInfo.position || 'N/A'}</p>`;
                    infoHTML += `<p><strong>Công ty:</strong> ${cardInfo.company || 'N/A'}</p>`;
                    infoHTML += `<p><strong>Email:</strong> ${cardInfo.email || 'N/A'}</p>`;
                    infoHTML += `<p><strong>Điện thoại:</strong> ${cardInfo.phone || 'N/A'}</p>`;
                    infoHTML += '</div>';
                    
                    document.getElementById('camera0-preview').innerHTML += infoHTML;
                    
                    // Chuyển sang bước tiếp theo
                    updateStep(4);
                } else {
                    document.getElementById('status').innerHTML = 'Lỗi khi trích xuất thông tin: ' + data.error;
                }
            })
            .catch(error => {
                console.error('Error extracting card info:', error);
                document.getElementById('status').innerHTML = 'Lỗi khi trích xuất thông tin';
            });
        }
        
        function generateAIImage() {
            if (!capturedImages[1] || !sessionId) {
                document.getElementById('status').innerHTML = 'Không có ảnh khuôn mặt hoặc phiên không hợp lệ';
                return;
            }
            
            updateStep(5);
            document.getElementById('status').innerHTML = 'Đang tạo ảnh AI...';
            
            // Gửi ảnh khuôn mặt để xử lý
            fetch('/capture_face', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ face_image: capturedImages[1] })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('status').innerHTML = 'Đã tải lên ảnh khuôn mặt thành công. Đang tạo ảnh AI...';
                    
                    // Tiếp tục tạo ảnh AI
                    fetch('/generate_ai_image', {
                        method: 'POST'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            document.getElementById('status').innerHTML = 'Đã tạo ảnh AI thành công!';
                            
                            // Hiển thị kết quả
                            document.getElementById('result-container').classList.remove('hidden');
                            
                            // Hiển thị tất cả các biến thể
                            let resultHTML = '';
                            data.image_paths.forEach((path, index) => {
                                resultHTML += `<div style="margin-bottom: 20px;">
                                    <h3>Biến thể ${index + 1}</h3>
                                    <img src="/${path}" class="result-image">
                                    <div class="btn-container">
                                        <a href="/download_session_image/${sessionId}/${index + 1}" class="btn" download>Tải xuống</a>
                                    </div>
                                </div>`;
                            });
                            
                            document.getElementById('result-image').innerHTML = resultHTML;
                        } else {
                            document.getElementById('status').innerHTML = 'Lỗi khi tạo ảnh AI: ' + data.error;
                        }
                    })
                    .catch(error => {
                        console.error('Error generating AI image:', error);
                        document.getElementById('status').innerHTML = 'Lỗi khi tạo ảnh AI';
                    });
                } else {
                    document.getElementById('status').innerHTML = 'Lỗi khi tải lên ảnh khuôn mặt: ' + data.error;
                }
            })
            .catch(error => {
                console.error('Error uploading face:', error);
                document.getElementById('status').innerHTML = 'Lỗi khi tải lên ảnh khuôn mặt';
            });
        }
        
        function startNewSession() {
            // Reset tất cả trạng thái
            sessionId = null;
            capturedImages = [null, null];
            
            // Reset UI
            document.getElementById('camera0-preview').classList.add('hidden');
            document.getElementById('camera1-preview').classList.add('hidden');
            document.getElementById('capture0-btn').classList.remove('hidden');
            document.getElementById('capture1-btn').classList.remove('hidden');
            document.getElementById('retake0-btn').classList.add('hidden');
            document.getElementById('retake1-btn').classList.add('hidden');
            document.getElementById('result-container').classList.add('hidden');
            
            // Bắt đầu phiên mới
            startSession();
        }
    </script>
</body>
</html>

