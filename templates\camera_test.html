<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            text-align: center;
            color: #333;
        }
        .camera-container {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
            gap: 20px;
        }
        .camera-box {
            width: 48%;
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .camera-feed {
            width: 100%;
            height: auto;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9f7fe;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .btn-danger {
            background-color: #e74c3c;
        }
        .btn-danger:hover {
            background-color: #c0392b;
        }
        .btn-success {
            background-color: #2ecc71;
        }
        .btn-success:hover {
            background-color: #27ae60;
        }
        .controls {
            margin-top: 15px;
            text-align: center;
        }
        .focus-control {
            margin: 15px 0;
            text-align: center;
        }
        .focus-control label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .preview-container {
            margin-top: 20px;
        }
        .preview-image {
            max-width: 100%;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .hidden {
            display: none;
        }
        .fps-info {
            text-align: center;
            margin-top: 5px;
            font-size: 12px;
            color: #666;
        }
        .main-controls {
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>Camera Test</h1>
    
    <div class="main-controls">
        <button id="check-btn" class="btn" onclick="checkCameras()">Kiểm tra camera</button>
        <a href="/" class="btn">Quay lại trang chính</a>
    </div>
    
    <div id="status" class="status">
        Trạng thái camera sẽ hiển thị ở đây...
    </div>
    
    <div class="camera-container">
        <div class="camera-box">
            <h2>Camera 0 (Logitech - Card)</h2>
            <img id="camera0" class="camera-feed" src="/video_feed/0" alt="Camera 0">
            <div class="fps-info">FPS: Đang đo...</div>
            <div class="focus-control">
                <label for="focus-slider">Điều chỉnh độ nét (Focus):</label>
                <input type="range" id="focus-slider" min="0" max="255" value="80" oninput="adjustFocus(this.value)">
                <span id="focus-value">80</span>
            </div>
            <div class="controls">
                <button id="capture0-btn" class="btn btn-success" onclick="captureImage(0)">Chụp ảnh</button>
                <button id="retake0-btn" class="btn btn-danger hidden" onclick="retakeImage(0)">Chụp lại</button>
                <button id="save0-btn" class="btn hidden" onclick="saveImage(0)">Lưu ảnh</button>
            </div>
            <div id="camera0-preview" class="preview-container hidden"></div>
        </div>
        
        <div class="camera-box">
            <h2>Camera 1 (Laptop - Face)</h2>
            <img id="camera1" class="camera-feed" src="/video_feed/1" alt="Camera 1">
            <div class="fps-info">FPS: Đang đo...</div>
            <div class="controls">
                <button id="capture1-btn" class="btn btn-success" onclick="captureImage(1)">Chụp ảnh</button>
                <button id="retake1-btn" class="btn btn-danger hidden" onclick="retakeImage(1)">Chụp lại</button>
                <button id="save1-btn" class="btn hidden" onclick="saveImage(1)">Lưu ảnh</button>
            </div>
            <div id="camera1-preview" class="preview-container hidden"></div>
        </div>
    </div>
    
    <script>
        // Biến lưu trữ đường dẫn ảnh đã chụp
        let capturedImages = [null, null];
        
        // Kiểm tra camera khi trang được tải
        window.onload = function() {
            checkCameras();
            
            // Cập nhật giá trị focus ban đầu
            document.getElementById('focus-value').textContent = document.getElementById('focus-slider').value;
        };
        
        function checkCameras() {
            document.getElementById('status').innerHTML = 'Đang kiểm tra camera...';
            
            fetch('/check_cameras')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('status').innerHTML = data.message;
                    
                    // Nếu camera không khả dụng, hiển thị thông báo
                    if (!data.camera0_status) {
                        document.getElementById('camera0').src = '';
                        document.getElementById('camera0').alt = 'Camera 0 không khả dụng';
                        document.getElementById('capture0-btn').disabled = true;
                    }
                    
                    if (!data.camera1_status) {
                        document.getElementById('camera1').src = '';
                        document.getElementById('camera1').alt = 'Camera 1 không khả dụng';
                        document.getElementById('capture1-btn').disabled = true;
                    }
                })
                .catch(error => {
                    document.getElementById('status').innerHTML = 'Lỗi khi kiểm tra camera: ' + error;
                });
        }
        
        function adjustFocus(value) {
            document.getElementById('focus-value').textContent = value;
            
            fetch('/adjust_focus', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ focus_value: parseInt(value) })
            })
            .then(response => response.json())
            .then(data => {
                console.log('Focus adjusted:', data);
                if (data.status === 'success') {
                    document.getElementById('status').innerHTML = data.message;
                }
            })
            .catch(error => {
                console.error('Error adjusting focus:', error);
                document.getElementById('status').innerHTML = 'Lỗi khi điều chỉnh focus: ' + error;
            });
        }
        
        function captureImage(cameraId) {
            fetch(`/capture_step/${cameraId}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    capturedImages[cameraId] = data.image_path;
                    
                    // Hiển thị ảnh đã chụp
                    const resultDiv = document.getElementById(`camera${cameraId}-preview`);
                    resultDiv.innerHTML = `<img src="/${data.image_path}" class="preview-image">`;
                    resultDiv.classList.remove('hidden');
                    
                    // Cập nhật trạng thái nút
                    document.getElementById(`capture${cameraId}-btn`).classList.add('hidden');
                    document.getElementById(`retake${cameraId}-btn`).classList.remove('hidden');
                    document.getElementById(`save${cameraId}-btn`).classList.remove('hidden');
                    
                    document.getElementById('status').innerHTML = `Đã chụp ảnh từ camera ${cameraId}`;
                } else {
                    document.getElementById('status').innerHTML = 'Lỗi khi chụp ảnh: ' + data.message;
                }
            })
            .catch(error => {
                console.error('Error capturing image:', error);
                document.getElementById('status').innerHTML = 'Lỗi khi chụp ảnh';
            });
        }
        
        function retakeImage(cameraId) {
            // Nếu có ảnh đã chụp, gửi yêu cầu xóa
            if (capturedImages[cameraId]) {
                fetch(`/retake_image/${cameraId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ previous_image: capturedImages[cameraId] })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // Reset trạng thái
                        capturedImages[cameraId] = null;
                        document.getElementById(`camera${cameraId}-preview`).classList.add('hidden');
                        document.getElementById(`capture${cameraId}-btn`).classList.remove('hidden');
                        document.getElementById(`retake${cameraId}-btn`).classList.add('hidden');
                        document.getElementById(`save${cameraId}-btn`).classList.add('hidden');
                        
                        document.getElementById('status').innerHTML = 'Sẵn sàng chụp lại ảnh';
                    }
                })
                .catch(error => {
                    console.error('Error retaking image:', error);
                    document.getElementById('status').innerHTML = 'Lỗi khi chuẩn bị chụp lại';
                });
            } else {
                // Nếu không có ảnh, chỉ cần reset UI
                document.getElementById(`camera${cameraId}-preview`).classList.add('hidden');
                document.getElementById(`capture${cameraId}-btn`).classList.remove('hidden');
                document.getElementById(`retake${cameraId}-btn`).classList.add('hidden');
                document.getElementById(`save${cameraId}-btn`).classList.add('hidden');
            }
        }
        
        function saveImage(cameraId) {
            if (!capturedImages[cameraId]) {
                document.getElementById('status').innerHTML = 'Không có ảnh để lưu';
                return;
            }
            
            const customName = prompt("Nhập tên file (không cần đuôi .jpg):", `camera${cameraId}_image`);
            if (!customName) return;
            
            const timestamp = Date.now();
            const filename = `static/img/${customName}_${timestamp}.jpg`;
            
            fetch(`/save_image/${cameraId}/${filename}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    document.getElementById('status').innerHTML = `Đã lưu ảnh thành ${filename}`;
                    
                    // Cập nhật đường dẫn ảnh đã lưu
                    capturedImages[cameraId] = filename;
                    
                    // Hiển thị thông báo thành công
                    alert(`Đã lưu ảnh thành công: ${filename}`);
                } else {
                    document.getElementById('status').innerHTML = 'Lỗi khi lưu ảnh: ' + data.message;
                }
            })
            .catch(error => {
                console.error('Error saving image:', error);
                document.getElementById('status').innerHTML = 'Lỗi khi lưu ảnh';
            });
        }
    </script>
</body>
</html>

