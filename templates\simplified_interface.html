<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Card Visit - <PERSON><PERSON><PERSON> di<PERSON>n đ<PERSON> g<PERSON>n</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }
        
        .status-bar {
            background: #fff;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .workflow-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            padding: 0 20px;
        }
        
        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            flex: 1;
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
        }
        
        .step-line {
            position: absolute;
            top: 20px;
            right: -50%;
            width: 100%;
            height: 3px;
            background: #ddd;
            z-index: 1;
        }
        
        .step:last-child .step-line {
            display: none;
        }
        
        .step-title {
            font-size: 0.9em;
            text-align: center;
            color: #7f8c8d;
            transition: all 0.3s ease;
        }
        
        .step.active .step-number {
            background: #3498db;
            color: white;
        }
        
        .step.active .step-title {
            color: #3498db;
            font-weight: bold;
        }
        
        .step.completed .step-number {
            background: #2ecc71;
            color: white;
        }
        
        .step.completed .step-line {
            background: #2ecc71;
        }
        
        .main-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }
        
        .camera-container {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .camera-box {
            flex: 1;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
        }
        
        .camera-title {
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
            color: #2c3e50;
        }
        
        .camera-feed {
            width: 100%;
            border-radius: 5px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .focus-control {
            margin-bottom: 15px;
        }
        
        .focus-control label {
            display: block;
            margin-bottom: 5px;
            font-size: 0.9em;
            color: #7f8c8d;
        }
        
        .focus-control input {
            width: 100%;
        }
        
        .btn-container {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: auto;
        }
        
        .preview-container {
            margin-top: 15px;
            text-align: center;
        }
        
        .preview-image {
            max-width: 100%;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .result-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-top: 30px;
        }
        
        .result-container h2 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 20px;
        }
        
        .result-image {
            max-width: 100%;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .hidden {
            display: none;
        }
        
        .loading {
            display: inline-block;
            width: 50px;
            height: 50px;
            border: 3px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: #3498db;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .card-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        
        .card-info p {
            margin: 5px 0;
        }
        
        .card-info strong {
            color: #2c3e50;
        }
        
        .screen {
            display: none;
        }
        
        .screen.active {
            display: block;
        }
        
        #input-screen {
            display: block;
        }
        
        #result-screen {
            display: none;
        }
        
        .info-overlay {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.7);
            border: 2px solid rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.5);
            max-width: 300px;
            font-size: 0.9em;
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
            color: white;
        }
        
        .info-overlay h4 {
            margin: 0 0 10px 0;
            color: #ffffff;
            font-size: 1em;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            padding-bottom: 5px;
        }
        
        .info-row {
            margin-bottom: 5px;
        }
        
        .info-label {
            font-weight: bold;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AI Card Visit Generator</h1>
            <p>Tạo ảnh AI từ thẻ visit và khuôn mặt</p>
        </div>
        
        <div class="status-bar" id="status">
            Vui lòng kiểm tra camera và bắt đầu phiên mới
        </div>
        
        <div class="workflow-steps">
            <div class="step active" id="step1">
                <div class="step-number">1</div>
                <div class="step-line"></div>
                <div class="step-title">Chụp ảnh</div>
            </div>
            <div class="step" id="step2">
                <div class="step-number">2</div>
                <div class="step-line"></div>
                <div class="step-title">Trích xuất thông tin</div>
            </div>
            <div class="step" id="step3">
                <div class="step-number">3</div>
                <div class="step-line"></div>
                <div class="step-title">Xác nhận thông tin</div>
            </div>
            <div class="step" id="step4">
                <div class="step-number">4</div>
                <div class="step-line"></div>
                <div class="step-title">Chụp khuôn mặt</div>
            </div>
            <div class="step" id="step5">
                <div class="step-number">5</div>
                <div class="step-title">Tạo ảnh AI</div>
            </div>
        </div>
        
        <div class="main-controls">
            <button id="check-btn" class="btn" onclick="checkCameras()">Kiểm tra camera</button>
            <button id="start-session-btn" class="btn btn-secondary" onclick="startSession()">Bắt đầu phiên mới</button>
        </div>
        
        <!-- Màn hình 1: Input với 2 camera -->
        <div id="input-screen" class="screen active">
            <div class="camera-container">
                <div class="camera-box">
                    <div class="camera-title">Camera 0 (Logitech - Card)</div>
                    <img id="camera0" class="camera-feed" src="/video_feed/0" alt="Camera 0">
                    <div class="focus-control">
                        <label for="focus-slider">Điều chỉnh độ nét (Focus):</label>
                        <input type="range" id="focus-slider" min="0" max="255" value="80" oninput="adjustFocus(this.value)">
                        <span id="focus-value">80</span>
                    </div>
                    <div class="btn-container">
                        <button id="capture0-btn" class="btn" onclick="captureImage(0)">Chụp ảnh thẻ</button>
                        <button id="retake0-btn" class="btn btn-danger hidden" onclick="retakeImage(0)">Chụp lại</button>
                    </div>
                    <div id="camera0-preview" class="preview-container hidden"></div>
                </div>
                
                <div class="camera-box">
                    <div class="camera-title">Camera 1 (Laptop - Face)</div>
                    <img id="camera1" class="camera-feed" src="/video_feed/1" alt="Camera 1">
                    <div class="btn-container">
                        <button id="capture1-btn" class="btn" onclick="captureImage(1)">Chụp khuôn mặt</button>
                        <button id="retake1-btn" class="btn btn-danger hidden" onclick="retakeImage(1)">Chụp lại</button>
                    </div>
                    <div id="camera1-preview" class="preview-container hidden"></div>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 20px;">
                <button id="generate-btn" class="btn" onclick="generateAIImage()" disabled>Tạo ảnh AI</button>
            </div>
        </div>
        
        <!-- Màn hình 2: Kết quả -->
        <div id="result-screen" class="screen">
            <div id="generation-status" style="text-align: center; margin-bottom: 20px;"></div>
            
            <div id="result-container" class="result-container hidden">
                <h2>Kết quả tạo ảnh AI</h2>
                <div id="image-navigation">
                    <h3>Ảnh AI đã tạo (<span id="current-image">1</span>/<span id="total-images">2</span>)</h3>
                    <div style="position: relative; text-align: center; margin: 20px 0;">
                        <img id="result-image" class="result-image" style="max-width: 100%;">
                        <div id="info-overlay" class="info-overlay">
                            <h4>📄 Thông tin thẻ</h4>
                            <div class="info-content">
                                <div class="info-row">
                                    <span class="info-label">👤 Tên:</span>
                                    <span class="info-value" id="overlay-name">-</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">🏢 Chức vụ:</span>
                                    <span class="info-value" id="overlay-position">-</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">🏢 Công ty:</span>
                                    <span class="info-value" id="overlay-company">-</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">📧 Email:</span>
                                    <span class="info-value" id="overlay-email">-</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">📱 Điện thoại:</span>
                                    <span class="info-value" id="overlay-phone">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="text-align: center; margin: 10px 0;">
                        <button class="btn" onclick="toggleInfoOverlay()">Hiện/Ẩn thông tin</button>
                        <button class="btn" onclick="prevImage()" id="prev-btn" disabled>Ảnh trước</button>
                        <button class="btn" onclick="nextImage()" id="next-btn">Ảnh tiếp theo</button>
                    </div>
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn" onclick="downloadCurrentImage()">Tải ảnh hiện tại</button>
                    <button class="btn" onclick="downloadAllImages()">Tải tất cả ảnh</button>
                    <button class="btn btn-secondary" onclick="startNewSession()">Phiên mới</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Biến lưu trữ thông tin phiên
        let sessionId = null;
        let capturedImages = [null, null];
        let currentStep = 1;
        let generatedImages = [];
        let currentImageIndex = 0;
        let cardInfo = null;
        
        // Kiểm tra camera khi trang được tải
        window.onload = function() {
            checkCameras();
            
            // Cập nhật giá trị focus ban đầu
            document.getElementById('focus-value').textContent = document.getElementById('focus-slider').value;
            
            // Ẩn overlay thông tin ban đầu
            document.getElementById('info-overlay').classList.add('hidden');
        };
        
        function checkCameras() {
            document.getElementById('status').innerHTML = 'Đang kiểm tra camera...';
            
            fetch('/check_cameras')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('status').innerHTML = data.message;
                    
                    // Nếu camera không khả dụng, hiển thị thông báo
                    if (!data.camera0_status) {
                        document.getElementById('camera0').src = '';
                        document.getElementById('camera0').alt = 'Camera 0 không khả dụng';
                        document.getElementById('capture0-btn').disabled = true;
                    }
                    
                    if (!data.camera1_status) {
                        document.getElementById('camera1').src = '';
                        document.getElementById('camera1').alt = 'Camera 1 không khả dụng';
                        document.getElementById('capture1-btn').disabled = true;
                    }
                })
                .catch(error => {
                    document.getElementById('status').innerHTML = 'Lỗi khi kiểm tra camera: ' + error;
                });
        }
        
        function adjustFocus(value) {
            document.getElementById('focus-value').textContent = value;
            
            fetch('/adjust_focus', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ focus_value: parseInt(value) })
            })
            .then(response => response.json())
            .then(data => {
                console.log('Focus adjusted:', data);
                if (data.status === 'success') {
                    document.getElementById('status').innerHTML = data.message;
                }
            })
            .catch(error => {
                console.error('Error adjusting focus:', error);
                document.getElementById('status').innerHTML = 'Lỗi khi điều chỉnh focus: ' + error;
            });
        }
        
        function startSession() {
            fetch('/start_session', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    sessionId = data.session_id;
                    document.getElementById('status').innerHTML = 'Phiên mới đã bắt đầu. Vui lòng chụp ảnh thẻ.';
                    updateStep(1);
                    
                    // Reset UI
                    capturedImages = [null, null];
                    document.getElementById('camera0-preview').classList.add('hidden');
                    document.getElementById('camera1-preview').classList.add('hidden');
                    document.getElementById('capture0-btn').classList.remove('hidden');
                    document.getElementById('capture1-btn').classList.remove('hidden');
                    document.getElementById('retake0-btn').classList.add('hidden');
                    document.getElementById('retake1-btn').classList.add('hidden');
                    document.getElementById('generate-btn').disabled = true;
                    
                    // Hiển thị màn hình input
                    document.getElementById('input-screen').classList.add('active');
                    document.getElementById('result-screen').classList.remove('active');
                } else {
                    document.getElementById('status').innerHTML = 'Lỗi khi bắt đầu phiên: ' + data.error;
                }
            })
            .catch(error => {
                console.error('Error starting session:', error);
                document.getElementById('status').innerHTML = 'Lỗi khi bắt đầu phiên';
            });
        }
        
        function updateStep(step) {
            // Reset all steps
            for (let i = 1; i <= 5; i++) {
                const stepElement = document.getElementById(`step${i}`);
                stepElement.classList.remove('active', 'completed');
                
                if (i < step) {
                    stepElement.classList.add('completed');
                } else if (i === step) {
                    stepElement.classList.add('active');
                }
            }
            
            currentStep = step;
        }
        
        function captureImage(cameraId) {
            fetch(`/capture_high_quality/${cameraId}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    capturedImages[cameraId] = data.image_path;
                    
                    // Hiển thị ảnh đã chụp
                    const resultDiv = document.getElementById(`camera${cameraId}-preview`);
                    resultDiv.innerHTML = `<img src="/${data.image_path}" class="preview-image">`;
                    resultDiv.classList.remove('hidden');
                    
                    // Cập nhật trạng thái nút
                    document.getElementById(`capture${cameraId}-btn`).classList.add('hidden');
                    document.getElementById(`retake${cameraId}-btn`).classList.remove('hidden');
                    
                