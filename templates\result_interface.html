<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Card Visit - <PERSON><PERSON><PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #28cde8 0%, #1e90ff 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .main-content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .result-section {
            display: flex;
            gap: 30px;
            margin-bottom: 30px;
        }

        .ai-image-container {
            flex: 2;
            position: relative;
        }

        .ai-image {
            width: 100%;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .info-overlay {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            max-width: 300px;
        }

        .info-overlay h3 {
            margin-bottom: 10px;
            color: #28cde8;
        }

        .info-item {
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .info-label {
            font-weight: bold;
            color: #ccc;
        }

        .input-images {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .input-image-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .input-image-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .input-image {
            width: 100%;
            max-height: 200px;
            object-fit: cover;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .card-info-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .card-info-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
            text-align: center;
        }

        .card-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .card-info-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .card-info-label {
            font-weight: bold;
            color: #666;
            margin-bottom: 5px;
        }

        .card-info-value {
            color: #333;
            font-size: 1.1rem;
        }

        .actions {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
        }

        .btn {
            background: linear-gradient(135deg, #28cde8, #1e90ff);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 205, 232, 0.3);
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 205, 232, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }

        .btn-secondary:hover {
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        }

        .error-container {
            text-align: center;
            padding: 50px;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }

        .success-badge {
            background: #d4edda;
            color: #155724;
            padding: 10px 20px;
            border-radius: 20px;
            display: inline-block;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .result-section {
                flex-direction: column;
            }
            
            .input-images {
                flex-direction: row;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .main-content {
                padding: 20px;
            }
            
            .input-images {
                flex-direction: column;
            }
            
            .actions {
                flex-direction: column;
                align-items: center;
            }
            
            .info-overlay {
                position: static;
                margin-top: 20px;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Kết Quả AI Card Visit</h1>
            <p>Ảnh AI dollhouse đã được tạo thành công</p>
        </div>

        <div class="main-content">
            {% if error %}
                <div class="error-container">
                    <div class="error-message">
                        <h3>❌ Có lỗi xảy ra</h3>
                        <p>{{ error }}</p>
                    </div>
                    <a href="/" class="btn">🔄 Thử lại</a>
                </div>
            {% elif success %}
                <div class="success-badge">
                    ✅ Tạo ảnh AI thành công!
                </div>

                <div class="result-section">
                    <div class="ai-image-container">
                        {% if ai_result.image_paths %}
                            <img src="/{{ ai_result.image_paths[0] }}" alt="AI Generated Image" class="ai-image">
                        {% elif ai_result.image_path %}
                            <img src="/{{ ai_result.image_path }}" alt="AI Generated Image" class="ai-image">
                        {% endif %}
                        
                        <!-- Info overlay giống khai.png -->
                        <div class="info-overlay">
                            <h3>📋 Thông tin</h3>
                            <div class="info-item">
                                <span class="info-label">Tên:</span> {{ card_info.name or 'N/A' }}
                            </div>
                            <div class="info-item">
                                <span class="info-label">Công ty:</span> {{ card_info.company or 'N/A' }}
                            </div>
                            <div class="info-item">
                                <span class="info-label">Chức vụ:</span> {{ card_info.position or 'N/A' }}
                            </div>
                            <div class="info-item">
                                <span class="info-label">Email:</span> {{ card_info.email or 'N/A' }}
                            </div>
                            <div class="info-item">
                                <span class="info-label">Điện thoại:</span> {{ card_info.phone or 'N/A' }}
                            </div>
                        </div>
                    </div>

                    <div class="input-images">
                        <div class="input-image-container">
                            <div class="input-image-title">📇 Visit Card</div>
                            <img src="/{{ card_image }}" alt="Business Card" class="input-image">
                        </div>
                        
                        <div class="input-image-container">
                            <div class="input-image-title">👤 Khuôn Mặt</div>
                            <img src="/{{ face_image }}" alt="Face Photo" class="input-image">
                        </div>
                    </div>
                </div>

                <!-- Card Info Details -->
                <div class="card-info-section">
                    <div class="card-info-title">📊 Chi tiết thông tin Visit Card</div>
                    <div class="card-info-grid">
                        <div class="card-info-item">
                            <div class="card-info-label">Họ tên</div>
                            <div class="card-info-value">{{ card_info.name or 'Không xác định' }}</div>
                        </div>
                        <div class="card-info-item">
                            <div class="card-info-label">Công ty</div>
                            <div class="card-info-value">{{ card_info.company or 'Không xác định' }}</div>
                        </div>
                        <div class="card-info-item">
                            <div class="card-info-label">Chức vụ</div>
                            <div class="card-info-value">{{ card_info.position or 'Không xác định' }}</div>
                        </div>
                        <div class="card-info-item">
                            <div class="card-info-label">Email</div>
                            <div class="card-info-value">{{ card_info.email or 'Không xác định' }}</div>
                        </div>
                        <div class="card-info-item">
                            <div class="card-info-label">Điện thoại</div>
                            <div class="card-info-value">{{ card_info.phone or 'Không xác định' }}</div>
                        </div>
                        <div class="card-info-item">
                            <div class="card-info-label">API sử dụng</div>
                            <div class="card-info-value">{{ ai_result.api_used or 'Gemini 2.0' }}</div>
                        </div>
                    </div>
                </div>

                <div class="actions">
                    <a href="/" class="btn">🔄 Tạo mới</a>
                    {% if ai_result.image_paths and ai_result.image_paths|length > 1 %}
                        <button class="btn btn-secondary" onclick="showNextImage()">🖼️ Xem ảnh khác</button>
                    {% endif %}
                    <button class="btn btn-secondary" onclick="downloadImage()">💾 Tải xuống</button>
                </div>
            {% endif %}
        </div>
    </div>

    <script>
        let currentImageIndex = 0;
        const imageList = {{ ai_result.image_paths | tojson if ai_result and ai_result.image_paths else '[]' }};

        function showNextImage() {
            if (imageList.length > 1) {
                currentImageIndex = (currentImageIndex + 1) % imageList.length;
                const aiImage = document.querySelector('.ai-image');
                aiImage.src = '/' + imageList[currentImageIndex] + '?t=' + new Date().getTime();
            }
        }

        function downloadImage() {
            const aiImage = document.querySelector('.ai-image');
            if (aiImage) {
                const link = document.createElement('a');
                link.href = aiImage.src;
                link.download = 'ai_dollhouse_' + Date.now() + '.png';
                link.click();
            }
        }
    </script>
</body>
</html>
