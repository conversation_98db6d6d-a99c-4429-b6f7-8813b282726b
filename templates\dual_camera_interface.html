<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Card Visit - Dual Camera Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #28cde8 0%, #1e90ff 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .main-content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        /* Camera Interface Styles */
        .camera-section {
            display: flex;
            justify-content: space-between;
            gap: 30px;
            margin-bottom: 30px;
        }

        .camera-container {
            flex: 1;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .camera-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .camera-title.card-camera {
            color: #28cde8;
        }

        .camera-title.face-camera {
            color: #1e90ff;
        }

        .camera-feed {
            width: 100%;
            max-width: 640px;
            height: 480px;
            border: 3px solid #ddd;
            border-radius: 10px;
            background: #000;
            margin-bottom: 15px;
            object-fit: cover;
        }

        .camera-feed.card-feed {
            border-color: #28cde8;
        }

        .camera-feed.face-feed {
            border-color: #1e90ff;
        }

        .camera-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .focus-control {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(40, 205, 232, 0.1);
            padding: 10px;
            border-radius: 10px;
            margin-top: 10px;
        }

        .focus-slider {
            width: 150px;
        }

        .capture-btn {
            background: linear-gradient(135deg, #28cde8, #1e90ff);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 205, 232, 0.3);
        }

        .capture-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 205, 232, 0.4);
        }

        .capture-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .retake-btn {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            border: none;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: none;
        }

        .retake-btn:hover {
            transform: translateY(-1px);
        }

        .capture-status {
            margin-top: 10px;
            font-weight: bold;
        }

        .capture-status.captured {
            color: #28a745;
        }

        .capture-status.pending {
            color: #ffc107;
        }

        /* Generate Section */
        .generate-section {
            text-align: center;
            padding: 30px;
            background: linear-gradient(135deg, rgba(40, 205, 232, 0.1), rgba(30, 144, 255, 0.1));
            border-radius: 15px;
            margin-top: 30px;
        }

        .generate-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 30px;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
            display: none;
        }

        .generate-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        }

        .generate-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .workflow-status {
            margin-bottom: 20px;
            font-size: 1.1rem;
            color: #666;
        }

        /* Loading Animation */
        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #28cde8;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .camera-section {
                flex-direction: column;
                gap: 20px;
            }
            
            .camera-container {
                max-width: 700px;
                margin: 0 auto;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .main-content {
                padding: 20px;
            }
            
            .camera-feed {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 AI Card Visit Generator</h1>
            <p>Chụp ảnh visit card và khuôn mặt để tạo ảnh AI dollhouse</p>
        </div>

        <div class="main-content">
            <!-- Camera Section -->
            <div class="camera-section">
                <!-- Camera 1: Business Card (Logitech C270) -->
                <div class="camera-container">
                    <div class="camera-title card-camera">📇 Camera Visit Card (Logitech C270)</div>
                    <img id="cam0" class="camera-feed card-feed" src="/video_feed/0" alt="Camera 0 - Business Card">
                    
                    <!-- Focus Control for Logitech -->
                    <div class="focus-control">
                        <label for="focusSlider">🔍 Chỉnh nét:</label>
                        <input type="range" id="focusSlider" class="focus-slider" min="0" max="255" value="0">
                        <span id="focusValue">0</span>
                    </div>
                    
                    <div class="camera-controls">
                        <button id="captureCard" class="capture-btn">📸 Chụp Visit Card</button>
                        <button id="retakeCard" class="retake-btn">🔄 Chụp lại</button>
                    </div>
                    
                    <div id="cardStatus" class="capture-status pending">⏳ Chưa chụp</div>
                </div>

                <!-- Camera 2: Face (Laptop Camera) -->
                <div class="camera-container">
                    <div class="camera-title face-camera">👤 Camera Khuôn Mặt (Laptop)</div>
                    <img id="cam1" class="camera-feed face-feed" src="/video_feed/1" alt="Camera 1 - Face">
                    
                    <div class="camera-controls">
                        <button id="captureFace" class="capture-btn" disabled>📸 Chụp Khuôn Mặt</button>
                        <button id="retakeFace" class="retake-btn">🔄 Chụp lại</button>
                    </div>
                    
                    <div id="faceStatus" class="capture-status pending">⏳ Chờ chụp visit card trước</div>
                </div>
            </div>

            <!-- Generate Section -->
            <div class="generate-section">
                <div id="workflowStatus" class="workflow-status">
                    📋 Bước 1: Chụp visit card trước, sau đó chụp khuôn mặt
                </div>
                
                <button id="generateBtn" class="generate-btn">
                    ✨ Tạo Ảnh AI Dollhouse
                </button>
                
                <div id="loadingSection" class="loading">
                    <div class="spinner"></div>
                    <p>Đang xử lý OCR và tạo ảnh AI...</p>
                </div>
            </div>
        </div>
    </div>
    <script>
        // Global variables
        let cardCaptured = false;
        let faceCaptured = false;
        let sessionId = null;

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeInterface();
            setupEventListeners();
        });

        function initializeInterface() {
            // Generate session ID
            sessionId = 'session_' + Date.now();
            console.log('🎯 Session ID:', sessionId);
        }

        function setupEventListeners() {
            // Focus control for Logitech camera
            const focusSlider = document.getElementById('focusSlider');
            const focusValue = document.getElementById('focusValue');

            focusSlider.addEventListener('input', function() {
                const value = this.value;
                focusValue.textContent = value;
                adjustFocus(value);
            });

            // Capture buttons
            document.getElementById('captureCard').addEventListener('click', () => captureImage(0, 'card'));
            document.getElementById('captureFace').addEventListener('click', () => captureImage(1, 'face'));

            // Retake buttons
            document.getElementById('retakeCard').addEventListener('click', () => retakeImage(0, 'card'));
            document.getElementById('retakeFace').addEventListener('click', () => retakeImage(1, 'face'));

            // Generate button
            document.getElementById('generateBtn').addEventListener('click', generateAI);
        }

        function adjustFocus(value) {
            fetch('/adjust_focus', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ focus_value: parseInt(value) })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    console.log('✅ Focus adjusted:', value);
                } else {
                    console.error('❌ Focus adjustment failed:', data.message);
                }
            })
            .catch(error => {
                console.error('❌ Focus adjustment error:', error);
            });
        }

        function captureImage(cameraId, type) {
            const captureBtn = document.getElementById(type === 'card' ? 'captureCard' : 'captureFace');
            const statusDiv = document.getElementById(type === 'card' ? 'cardStatus' : 'faceStatus');
            const retakeBtn = document.getElementById(type === 'card' ? 'retakeCard' : 'retakeFace');

            captureBtn.disabled = true;
            statusDiv.textContent = '📸 Đang chụp...';
            statusDiv.className = 'capture-status pending';

            fetch(`/capture_step/${cameraId}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Update UI
                    const imgElement = document.getElementById(`cam${cameraId}`);
                    imgElement.src = '/' + data.image_path + '?t=' + new Date().getTime();

                    statusDiv.textContent = '✅ Đã chụp thành công';
                    statusDiv.className = 'capture-status captured';
                    retakeBtn.style.display = 'inline-block';

                    // Update capture status
                    if (type === 'card') {
                        cardCaptured = true;
                        // Enable face capture
                        document.getElementById('captureFace').disabled = false;
                        document.getElementById('faceStatus').textContent = '⏳ Sẵn sàng chụp khuôn mặt';
                        updateWorkflowStatus();
                    } else if (type === 'face') {
                        faceCaptured = true;
                        updateWorkflowStatus();
                    }
                } else {
                    statusDiv.textContent = '❌ Lỗi: ' + data.message;
                    statusDiv.className = 'capture-status pending';
                    captureBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error('❌ Capture error:', error);
                statusDiv.textContent = '❌ Lỗi kết nối';
                statusDiv.className = 'capture-status pending';
                captureBtn.disabled = false;
            });
        }

        function retakeImage(cameraId, type) {
            const imgElement = document.getElementById(`cam${cameraId}`);
            const captureBtn = document.getElementById(type === 'card' ? 'captureCard' : 'captureFace');
            const statusDiv = document.getElementById(type === 'card' ? 'cardStatus' : 'faceStatus');
            const retakeBtn = document.getElementById(type === 'card' ? 'retakeCard' : 'retakeFace');

            // Restore video stream
            imgElement.src = `/video_feed/${cameraId}?t=` + new Date().getTime();

            // Reset UI
            captureBtn.disabled = false;
            retakeBtn.style.display = 'none';
            statusDiv.textContent = '⏳ Sẵn sàng chụp';
            statusDiv.className = 'capture-status pending';

            // Update capture status
            if (type === 'card') {
                cardCaptured = false;
                // Disable face capture if card is retaken
                document.getElementById('captureFace').disabled = true;
                document.getElementById('faceStatus').textContent = '⏳ Chờ chụp visit card trước';
                if (faceCaptured) {
                    // Also reset face if it was captured
                    retakeImage(1, 'face');
                }
            } else if (type === 'face') {
                faceCaptured = false;
            }

            updateWorkflowStatus();
        }

        function updateWorkflowStatus() {
            const workflowStatus = document.getElementById('workflowStatus');
            const generateBtn = document.getElementById('generateBtn');

            if (!cardCaptured) {
                workflowStatus.textContent = '📋 Bước 1: Chụp visit card trước';
                generateBtn.style.display = 'none';
            } else if (!faceCaptured) {
                workflowStatus.textContent = '📋 Bước 2: Chụp khuôn mặt';
                generateBtn.style.display = 'none';
            } else {
                workflowStatus.textContent = '✅ Hoàn thành! Sẵn sàng tạo ảnh AI';
                generateBtn.style.display = 'inline-block';
            }
        }

        function generateAI() {
            if (!cardCaptured || !faceCaptured) {
                alert('⚠️ Vui lòng chụp cả visit card và khuôn mặt trước!');
                return;
            }

            const generateBtn = document.getElementById('generateBtn');
            const loadingSection = document.getElementById('loadingSection');

            generateBtn.style.display = 'none';
            loadingSection.style.display = 'block';

            // Redirect to result page with session processing
            window.location.href = `/process_dual_camera?session_id=${sessionId}`;
        }
    </script>
</body>
</html>
