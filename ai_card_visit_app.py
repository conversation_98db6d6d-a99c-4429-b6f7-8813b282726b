#!/usr/bin/env python3
"""
AI Card Visit Application - New workflow implementation
Quy trình: Card → Check Info → Confirm → Face → AI Generation
"""

from flask import Flask, render_template, request, jsonify, send_file, session, Response
import os
import json
import time
from pathlib import Path
from datetime import datetime
from werkzeug.utils import secure_filename
from workflow_controller import WorkflowController
import cv2
import threading
import time

app = Flask(__name__)
app.secret_key = 'ai_card_visit_secret_key_2024'

# Configuration
UPLOAD_FOLDER = 'uploads'
SESSIONS_FOLDER = 'sessions'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp'}

# Create directories
Path(UPLOAD_FOLDER).mkdir(exist_ok=True)
Path(SESSIONS_FOLDER).mkdir(exist_ok=True)

# Initialize workflow controller
workflow_controller = WorkflowController(mode="testing")
print(f"🔥 Using: Gemini 2.0 Flash Preview Image Generation")

# Camera streaming variables (reuse from project_directory)
camera_0 = None  # Logitech (left)
camera_1 = None  # Laptop (right)
output_frame_0 = None
output_frame_1 = None
lock_0 = threading.Lock()
lock_1 = threading.Lock()
initial_focus_value = 0  # Logitech manual focus

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Camera functions - FIXED FOR VIDEO DISPLAY
def initialize_camera(camera_index, focus_value=None):
    """Initialize camera with proper warm-up and settings"""
    import time
    print(f"🔍 Initializing camera {camera_index}...")

    # Try CAP_DSHOW first (best for Windows)
    cap = cv2.VideoCapture(camera_index, cv2.CAP_DSHOW)
    time.sleep(1)  # Allow camera to warm up
    if not cap.isOpened():
        print(f"⚠️ CAP_DSHOW failed, trying default...")
        cap = cv2.VideoCapture(camera_index)
        time.sleep(1)

    if not cap.isOpened():
        print(f"❌ Cannot open camera {camera_index}")
        return None

    # Set desired resolution and frame rate
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    cap.set(cv2.CAP_PROP_FPS, 60)

    # Confirm actual FPS
    actual_fps = cap.get(cv2.CAP_PROP_FPS)
    print(f"Camera {camera_index} - FPS thực tế: {actual_fps:.2f}")

    # Set focus if provided
    if focus_value is not None:
        try:
            cap.set(cv2.CAP_PROP_AUTOFOCUS, 0)
            cap.set(cv2.CAP_PROP_FOCUS, focus_value)
            print(f"🔧 Camera {camera_index} - Focus set to {focus_value}")
        except Exception:
            pass

    return cap

def generate_frames(camera_id):
    """Generate frames for video streaming with proper error handling"""
    global camera_0, camera_1, output_frame_0, output_frame_1

    retry_count = 0
    max_retries = 3

    while True:
        cap = camera_0 if camera_id == 0 else camera_1
        lock = lock_0 if camera_id == 0 else lock_1

        if cap is None or not cap.isOpened():
            retry_count += 1
            if retry_count <= max_retries:
                print(f"🔄 Retrying camera {camera_id} initialization ({retry_count}/{max_retries})")
                if camera_id == 0:
                    with lock_0:
                        camera_0 = initialize_camera(0, initial_focus_value)
                else:
                    with lock_1:
                        camera_1 = initialize_camera(1)
                time.sleep(1)
                continue
            else:
                print(f"❌ Camera {camera_id} failed after {max_retries} retries")
                time.sleep(5)
                retry_count = 0
                continue

        try:
            ret, frame = cap.read()
            if not ret or frame is None:
                print(f"⚠️ Camera {camera_id} read failed, reinitializing...")
                cap.release()
                if camera_id == 0:
                    with lock_0:
                        camera_0 = initialize_camera(0, initial_focus_value)
                else:
                    with lock_1:
                        camera_1 = initialize_camera(1)
                time.sleep(1)
                continue

            # Reset retry count on successful read
            retry_count = 0

            # Store frame for capture
            with lock:
                if camera_id == 0:
                    output_frame_0 = frame.copy()
                else:
                    output_frame_1 = frame.copy()

            # Encode frame for streaming
            ret, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
            if not ret:
                continue

            frame_bytes = buffer.tobytes()
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')

        except Exception as e:
            print(f"❌ Error in camera {camera_id}: {e}")
            time.sleep(0.1)
            continue

@app.route('/')
def index():
    """Main page - New dual camera interface"""
    return render_template('dual_camera_interface.html')

@app.route('/old')
def old_interface():
    """Old interface (backup)"""
    return render_template('ai_card_visit.html')

@app.route('/new')
def new_interface():
    """New 2-screen interface with dual camera streaming"""
    return render_template('new_interface.html')

# Thêm route mới để xử lý quy trình đơn giản hóa
@app.route('/simplified')
def simplified_interface():
    """Giao diện đơn giản hóa với 2 màn hình chính"""
    return render_template('simplified_interface.html')

# Camera streaming routes
@app.route('/video_feed/<int:camera_id>')
def video_feed(camera_id):
    """Video streaming route for cameras"""
    return Response(generate_frames(camera_id),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/uploads/<filename>')
def uploaded_file(filename):
    """Serve uploaded files"""
    return send_file(os.path.join(UPLOAD_FOLDER, filename))

@app.route('/camera_debug')
def camera_debug():
    """Debug camera status"""
    global camera_0, camera_1, output_frame_0, output_frame_1

    status = {
        'camera_0': {
            'initialized': camera_0 is not None,
            'opened': camera_0.isOpened() if camera_0 else False,
            'frame_available': output_frame_0 is not None
        },
        'camera_1': {
            'initialized': camera_1 is not None,
            'opened': camera_1.isOpened() if camera_1 else False,
            'frame_available': output_frame_1 is not None
        }
    }

    return jsonify(status)

@app.route('/capture_step/<int:camera_id>', methods=['POST'])
def capture_step(camera_id):
    """Capture image from specific camera - FROM PROJECT_DIRECTORY"""
    global output_frame_0, output_frame_1
    timestamp = int(time.time())
    filename = None

    if camera_id == 0:
        with lock_0:
            if output_frame_0 is not None:
                filename = os.path.join(UPLOAD_FOLDER, f'logitech_left_{timestamp}.jpg')
                cv2.imwrite(filename, output_frame_0)
                print(f"✅ Captured camera 0: {filename}")
    elif camera_id == 1:
        with lock_1:
            if output_frame_1 is not None:
                filename = os.path.join(UPLOAD_FOLDER, f'laptop_right_{timestamp}.jpg')
                cv2.imwrite(filename, output_frame_1)
                print(f"✅ Captured camera 1: {filename}")

    if filename:
        return jsonify({'status': 'success', 'image_path': filename})
    else:
        return jsonify({'status': 'error', 'message': 'Không thể chụp ảnh.'}), 500

@app.route('/adjust_focus', methods=['POST'])
def adjust_focus():
    """Adjust focus for Logitech camera - FROM PROJECT_DIRECTORY"""
    global camera_0, initial_focus_value
    data = request.get_json()
    new_focus_value = data.get('focus_value')

    if new_focus_value is None:
        return jsonify({'status': 'error', 'message': 'Thiếu giá trị focus.'}), 400

    try:
        new_focus_value = int(new_focus_value)
    except ValueError:
        return jsonify({'status': 'error', 'message': 'Giá trị focus không hợp lệ.'}), 400

    with lock_0:
        if camera_0 and camera_0.isOpened():
            camera_0.set(cv2.CAP_PROP_FOCUS, new_focus_value)
            initial_focus_value = new_focus_value
            print(f"Focus mới cho Logitech: {new_focus_value}")
            return jsonify({'status': 'success', 'message': f'Đã chỉnh nét thành {new_focus_value}.'})
        else:
            return jsonify({'status': 'error', 'message': 'Camera Logitech không hoạt động.'}), 500

@app.route('/process_dual_camera')
def process_dual_camera():
    """Process dual camera captured images and generate AI"""
    session_id = request.args.get('session_id', 'default')
    print(f"🎯 Processing dual camera for session: {session_id}")

    try:
        # Find the latest captured images (using project_directory naming)
        card_files = [f for f in os.listdir(UPLOAD_FOLDER) if f.startswith('logitech_left_')]
        face_files = [f for f in os.listdir(UPLOAD_FOLDER) if f.startswith('laptop_right_')]

        if not card_files or not face_files:
            return render_template('result_interface.html',
                                 error="Không tìm thấy ảnh đã chụp. Vui lòng chụp lại.")

        # Get latest files
        card_file = max(card_files, key=lambda x: os.path.getctime(os.path.join(UPLOAD_FOLDER, x)))
        face_file = max(face_files, key=lambda x: os.path.getctime(os.path.join(UPLOAD_FOLDER, x)))

        card_path = os.path.join(UPLOAD_FOLDER, card_file)
        face_path = os.path.join(UPLOAD_FOLDER, face_file)

        print(f"🎯 Processing dual camera images:")
        print(f"   Card: {card_path}")
        print(f"   Face: {face_path}")

        # Process OCR
        print("📝 Starting OCR processing...")
        card_info = workflow_controller.ocr_service.extract_card_info(card_path)

        # Generate AI image
        print("🎨 Starting AI image generation...")
        ai_result = workflow_controller.ai_generator.generate_dollhouse_image(face_path, card_info)

        if ai_result.get('success'):
            return render_template('result_interface.html',
                                 success=True,
                                 card_info=card_info,
                                 ai_result=ai_result,
                                 card_image=card_path,
                                 face_image=face_path)
        else:
            return render_template('result_interface.html',
                                 error=f"Lỗi tạo ảnh AI: {ai_result.get('error', 'Unknown error')}")

    except Exception as e:
        print(f"❌ Error in dual camera processing: {e}")
        return render_template('result_interface.html',
                             error=f"Lỗi xử lý: {str(e)}")

# Function setup_cameras đã được định nghĩa ở cuối file

# Thêm route để xử lý retake image
@app.route('/retake_image/<int:camera_id>', methods=['POST'])
def retake_image(camera_id):
    """Xử lý yêu cầu chụp lại ảnh"""
    print(f"🔄 Retaking image for camera {camera_id}")
    data = request.get_json()
    previous_image = data.get('previous_image')
    
    # Xóa ảnh cũ nếu có
    if previous_image and os.path.exists(previous_image):
        try:
            os.remove(previous_image)
            print(f"✅ Đã xóa ảnh cũ: {previous_image}")
        except Exception as e:
            print(f"⚠️ Lỗi khi xóa ảnh cũ: {str(e)}")
    
    return jsonify({'status': 'success', 'message': 'Đã chuẩn bị chụp lại ảnh'})

@app.route('/start_session', methods=['POST'])
def start_session():
    """Start new session"""
    try:
        session_id = workflow_controller.start_new_session()
        session['current_session'] = session_id
        
        return jsonify({
            'success': True,
            'session_id': session_id,
            'message': 'New session started successfully'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/upload_card', methods=['POST'])
def upload_card():
    """Step 1: Upload business card image"""
    try:
        # Get current session
        session_id = session.get('current_session')
        if not session_id:
            session_id = workflow_controller.start_new_session()
            session['current_session'] = session_id
        
        card_path = None
        
        # Trường hợp 1: Có file upload trực tiếp
        if 'card_image' in request.files:
            file = request.files['card_image']
            if file.filename != '' and allowed_file(file.filename):
                # Save uploaded file with secure filename
                secure_name = secure_filename(file.filename)
                print(f"📁 Uploading file: {secure_name}")
                session_folder = Path(SESSIONS_FOLDER) / session_id
                session_folder.mkdir(exist_ok=True)

                card_path = str(session_folder / "card.jpg")
                file.save(card_path)
        
        # Trường hợp 2: Sử dụng đường dẫn ảnh đã chụp từ camera
        elif request.json and request.json.get('card_image'):
            image_path = request.json.get('card_image')
            # Kiểm tra file tồn tại
            if os.path.exists(image_path):
                # Lưu ảnh vào session folder
                session_folder = Path(SESSIONS_FOLDER) / session_id
                session_folder.mkdir(exist_ok=True)
                
                card_path = str(session_folder / "card.jpg")
                import shutil
                shutil.copy2(image_path, card_path)
        
        if card_path:
            # Update workflow
            workflow_controller.current_session['session_id'] = session_id
            workflow_controller.current_session['card_path'] = card_path
            
            return jsonify({
                'success': True,
                'card_path': card_path,
                'message': 'Card image uploaded successfully'
            })
        else:
            return jsonify({'success': False, 'error': 'No valid card image provided'})
            
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/extract_card_info', methods=['POST'])
def extract_card_info():
    """Step 2: Extract card information"""
    try:
        session_id = session.get('current_session')
        if not session_id or not workflow_controller.current_session['card_path']:
            return jsonify({'success': False, 'error': 'No card image available'})
        
        # Extract information
        card_info = workflow_controller.ocr_service.extract_text_from_card(
            workflow_controller.current_session['card_path']
        )
        
        if card_info:
            workflow_controller.current_session['card_info'] = card_info
            
            # Save to session folder
            session_folder = Path(SESSIONS_FOLDER) / session_id
            info_file = session_folder / "card_info.json"
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(card_info, f, ensure_ascii=False, indent=2)
            
            return jsonify({
                'success': True,
                'card_info': card_info,
                'message': 'Card information extracted successfully'
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to extract card information'})
            
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/update_card_info', methods=['POST'])
def update_card_info():
    """Step 3: Update/confirm card information"""
    try:
        session_id = session.get('current_session')
        if not session_id:
            return jsonify({'success': False, 'error': 'No active session'})
        
        # Get updated info from request
        updated_info = request.json
        
        # Update workflow
        workflow_controller.current_session['card_info'] = updated_info
        
        # Save updated info
        session_folder = Path(SESSIONS_FOLDER) / session_id
        info_file = session_folder / "card_info.json"
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(updated_info, f, ensure_ascii=False, indent=2)
        
        return jsonify({
            'success': True,
            'card_info': updated_info,
            'message': 'Card information updated successfully'
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/capture_face', methods=['POST'])
def capture_face():
    """Step 4: Capture face from camera (direct capture)"""
    try:
        session_id = session.get('current_session')
        if not session_id:
            return jsonify({'success': False, 'error': 'No active session'})

        # Xử lý ảnh đã chụp từ camera
        face_path = None
        
        # Trường hợp 1: Có file upload trực tiếp
        if 'face_image' in request.files:
            file = request.files['face_image']
            if file.filename != '':
                face_path = workflow_controller.save_captured_face(file, session_id)
        
        # Trường hợp 2: Sử dụng đường dẫn ảnh đã chụp từ camera
        elif request.json and request.json.get('face_image'):
            image_path = request.json.get('face_image')
            # Kiểm tra file tồn tại
            if os.path.exists(image_path):
                # Lưu ảnh vào session folder
                session_folder = Path(SESSIONS_FOLDER) / session_id
                session_folder.mkdir(exist_ok=True)
                
                face_path = str(session_folder / "face.jpg")
                import shutil
                shutil.copy2(image_path, face_path)
                
                # Cập nhật workflow
                workflow_controller.current_session['face_path'] = face_path
        
        if face_path:
            return jsonify({
                'success': True,
                'face_path': face_path,
                'message': 'Face captured and saved successfully'
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to save captured face'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/generate_ai_image', methods=['POST'])
def generate_ai_image():
    """Step 5: Generate AI image with integrated text"""
    try:
        session_id = session.get('current_session')
        if not session_id:
            return jsonify({'success': False, 'error': 'No active session'})
        
        # Check if we have all required data
        if not workflow_controller.current_session.get('face_path') or not workflow_controller.current_session.get('card_info'):
            return jsonify({'success': False, 'error': 'Missing face photo or card information'})
        
        # Generate AI image
        result = workflow_controller.ai_generator.generate_dollhouse_image(
            workflow_controller.current_session['face_path'],
            workflow_controller.current_session['card_info']
        )
        
        if result['success']:
            # Lưu kết quả vào session
            workflow_controller.current_session['ai_result'] = result
            
            # Trả về đường dẫn ảnh để hiển thị
            image_paths = []
            if 'session_image_paths' in result:
                image_paths = result['session_image_paths']
            elif 'session_primary_path' in result:
                image_paths = [result['session_primary_path']]
            elif 'image_paths' in result:
                # Copy images to session folder
                session_folder = Path(SESSIONS_FOLDER) / session_id
                session_folder.mkdir(exist_ok=True)

                session_image_paths = []
                for i, img_path in enumerate(result['image_paths']):
                    if os.path.exists(img_path):
                        session_img_path = session_folder / f"ai_dollhouse_{session_id}_v{i+1}.png"
                        import shutil
                        shutil.copy2(img_path, session_img_path)
                        session_image_paths.append(str(session_img_path))

                image_paths = session_image_paths
            
            return jsonify({
                'success': True,
                'message': 'AI image generated successfully',
                'image_paths': image_paths,
                'session_summary': {
                    'session_id': session_id,
                    'card_info': workflow_controller.current_session['card_info']
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            })
            
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/download_image/<session_id>')
def download_image(session_id):
    """Download primary generated AI image (backward compatibility)"""
    try:
        session_folder = Path(SESSIONS_FOLDER) / session_id
        # Try new format first
        image_path = session_folder / f"ai_dollhouse_{session_id}_v1.png"
        if not image_path.exists():
            # Fallback to old format
            image_path = session_folder / f"ai_dollhouse_{session_id}.png"

        if image_path.exists():
            return send_file(str(image_path), as_attachment=True)
        else:
            return jsonify({'error': 'Image not found'}), 404

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/download_session_image/<session_id>/<int:image_number>')
def download_session_image(session_id, image_number):
    """Download specific session image by number"""
    try:
        session_folder = Path(SESSIONS_FOLDER) / session_id
        image_path = session_folder / f"ai_dollhouse_{session_id}_v{image_number}.png"

        if image_path.exists():
            return send_file(str(image_path), as_attachment=True)
        else:
            return jsonify({'error': f'Image {image_number} not found'}), 404

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/get_session_info')
def get_session_info():
    """Get current session information"""
    try:
        session_id = session.get('current_session')
        if session_id:
            return jsonify({
                'success': True,
                'session_info': workflow_controller.get_session_info()
            })
        else:
            return jsonify({'success': False, 'error': 'No active session'})
            
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/switch_to_production', methods=['POST'])
def switch_to_production():
    """Switch to production mode"""
    try:
        workflow_controller.switch_to_production_mode()
        return jsonify({
            'success': True,
            'message': 'Switched to production mode with external cameras'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/system_status')
def system_status():
    """Get system status"""
    try:
        camera_status = workflow_controller.camera_manager.check_availability()
        
        return jsonify({
            'success': True,
            'mode': workflow_controller.mode,
            'cameras_available': camera_status,
            'gemini_available': bool(workflow_controller.ocr_service.gemini_key),
            'ai_generator_ready': True
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/camera_test')
def camera_test():
    """Test camera connection"""
    return render_template('camera_test.html')

@app.route('/check_cameras')
def check_cameras():
    """Check camera availability"""
    global camera_0, camera_1
    
    # Thử khởi tạo lại camera nếu chưa sẵn sàng
    if camera_0 is None or not camera_0.isOpened():
        camera_0 = initialize_camera(0, initial_focus_value)
    
    if camera_1 is None or not camera_1.isOpened():
        camera_1 = initialize_camera(1)
    
    # Kiểm tra trạng thái
    camera0_status = camera_0 is not None and camera_0.isOpened()
    camera1_status = camera_1 is not None and camera_1.isOpened()
    
    return jsonify({
        'camera0_status': camera0_status,
        'camera1_status': camera1_status,
        'message': f"Camera 0 (Logitech): {'Sẵn sàng' if camera0_status else 'Không khả dụng'}, "
                  f"Camera 1 (Laptop): {'Sẵn sàng' if camera1_status else 'Không khả dụng'}"
    })

# Duplicate initialize_camera function removed - using project_directory version above

# OLD generate_frames function removed - using project_directory version above

# Duplicate route removed - already defined above

# Duplicate capture_step route removed - already defined above

@app.route('/capture_high_quality/<int:camera_id>', methods=['POST'])
def capture_high_quality(camera_id):
    """Chụp ảnh với chất lượng cao hơn"""
    global output_frame_0, output_frame_1
    timestamp = int(time.time())
    filename = None
    
    # Tạo thư mục nếu chưa tồn tại
    os.makedirs(UPLOAD_FOLDER, exist_ok=True)
    
    if camera_id == 0:
        with lock_0:
            if output_frame_0 is not None:
                # Tăng độ phân giải nếu có thể
                try:
                    if camera_0 and camera_0.isOpened():
                        # Lưu cài đặt hiện tại
                        current_width = camera_0.get(cv2.CAP_PROP_FRAME_WIDTH)
                        current_height = camera_0.get(cv2.CAP_PROP_FRAME_HEIGHT)
                        
                        # Tăng độ phân giải tạm thời
                        camera_0.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
                        camera_0.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
                        
                        # Đọc frame chất lượng cao
                        ret, high_res_frame = camera_0.read()
                        
                        # Khôi phục cài đặt
                        camera_0.set(cv2.CAP_PROP_FRAME_WIDTH, current_width)
                        camera_0.set(cv2.CAP_PROP_FRAME_HEIGHT, current_height)
                        
                        if ret:
                            filename = os.path.join(UPLOAD_FOLDER, f'logitech_hq_{timestamp}.jpg')
                            # Lưu với chất lượng JPEG cao hơn (95%)
                            cv2.imwrite(filename, high_res_frame, [cv2.IMWRITE_JPEG_QUALITY, 95])
                            print(f"✅ Đã chụp ảnh chất lượng cao: {filename}")
                        else:
                            # Fallback to normal quality
                            filename = os.path.join(UPLOAD_FOLDER, f'logitech_left_{timestamp}.jpg')
                            cv2.imwrite(filename, output_frame_0, [cv2.IMWRITE_JPEG_QUALITY, 90])
                    else:
                        # Fallback to normal quality
                        filename = os.path.join(UPLOAD_FOLDER, f'logitech_left_{timestamp}.jpg')
                        cv2.imwrite(filename, output_frame_0, [cv2.IMWRITE_JPEG_QUALITY, 90])
                except Exception as e:
                    print(f"⚠️ Lỗi khi chụp ảnh chất lượng cao: {str(e)}")
                    # Fallback to normal quality
                    filename = os.path.join(UPLOAD_FOLDER, f'logitech_left_{timestamp}.jpg')
                    cv2.imwrite(filename, output_frame_0, [cv2.IMWRITE_JPEG_QUALITY, 90])
    elif camera_id == 1:
        with lock_1:
            if output_frame_1 is not None:
                try:
                    if camera_1 and camera_1.isOpened():
                        # Lưu cài đặt hiện tại
                        current_width = camera_1.get(cv2.CAP_PROP_FRAME_WIDTH)
                        current_height = camera_1.get(cv2.CAP_PROP_FRAME_HEIGHT)
                        
                        # Tăng độ phân giải tạm thời
                        camera_1.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
                        camera_1.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
                        
                        # Đọc frame chất lượng cao
                        ret, high_res_frame = camera_1.read()
                        
                        # Khôi phục cài đặt
                        camera_1.set(cv2.CAP_PROP_FRAME_WIDTH, current_width)
                        camera_1.set(cv2.CAP_PROP_FRAME_HEIGHT, current_height)
                        
                        if ret:
                            filename = os.path.join(UPLOAD_FOLDER, f'laptop_hq_{timestamp}.jpg')
                            # Lưu với chất lượng JPEG cao hơn (95%)
                            cv2.imwrite(filename, high_res_frame, [cv2.IMWRITE_JPEG_QUALITY, 95])
                            print(f"✅ Đã chụp ảnh chất lượng cao: {filename}")
                        else:
                            # Fallback to normal quality
                            filename = os.path.join(UPLOAD_FOLDER, f'laptop_right_{timestamp}.jpg')
                            cv2.imwrite(filename, output_frame_1, [cv2.IMWRITE_JPEG_QUALITY, 90])
                    else:
                        # Fallback to normal quality
                        filename = os.path.join(UPLOAD_FOLDER, f'laptop_right_{timestamp}.jpg')
                        cv2.imwrite(filename, output_frame_1, [cv2.IMWRITE_JPEG_QUALITY, 90])
                except Exception as e:
                    print(f"⚠️ Lỗi khi chụp ảnh chất lượng cao: {str(e)}")
                    # Fallback to normal quality
                    filename = os.path.join(UPLOAD_FOLDER, f'laptop_right_{timestamp}.jpg')
                    cv2.imwrite(filename, output_frame_1, [cv2.IMWRITE_JPEG_QUALITY, 90])
    if filename:
        return jsonify({'status': 'success', 'image_path': filename})
    else:
        return jsonify({'status': 'error', 'message': 'Cannot capture image.'}), 500

# Duplicate adjust_focus route removed - already defined above

def setup_cameras():
    """Setup cameras - CLEAN VERSION"""
    global camera_0, camera_1
    print("🎥 Initializing cameras...")

    camera_0 = initialize_camera(0, initial_focus_value)
    camera_1 = initialize_camera(1)

    # Start streaming threads
    threading.Thread(target=lambda: list(generate_frames(0)), daemon=True).start()
    threading.Thread(target=lambda: list(generate_frames(1)), daemon=True).start()

    # Status check
    camera0_status = camera_0 is not None and camera_0.isOpened()
    camera1_status = camera_1 is not None and camera_1.isOpened()

    print(f"📊 Camera Status:")
    print(f"   - Camera 0 (Logitech): {'✅ Ready' if camera0_status else '❌ Not available'}")
    print(f"   - Camera 1 (Laptop): {'✅ Ready' if camera1_status else '❌ Not available'}")

    return camera0_status or camera1_status

if __name__ == '__main__':
    print("🚀 Starting AI Card Visit Application")
    print("=" * 50)
    print("🌐 Access: http://localhost:5000")
    print("🧪 Mode: Testing (File import + Webcam)")
    print("🔄 Switch to production mode via /switch_to_production")
    print("🔍 Camera test page: http://localhost:5000/camera_test")
    
    # Khởi tạo camera trước khi chạy ứng dụng
    with app.app_context():
        camera_status = setup_cameras()
        if not camera_status:
            print("⚠️ Cảnh báo: Không thể kết nối với tất cả camera. Vui lòng kiểm tra lại.")
            print("🔍 Truy cập http://localhost:5000/camera_test để kiểm tra camera")
    
    # Chạy ứng dụng Flask
    app.run(debug=True, host='0.0.0.0', port=5000)





